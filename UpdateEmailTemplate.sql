-- Update the User Invitation Email Template to remove the name parameter
-- and fix the parameter indices

UPDATE [Internal].[EmailTemplate] 
SET 
    [Body] = '<!DOCTYPE html><html><body>Hello,<br/><br/>Congratulations!<br/><br/>You have been invited to WHO-MALARIA Surveillance Toolkit.<br/>Kindly accept the invitation and begin using the application for country {0} by logging in.<br/><br/>Click below link to log in:<br/><br/><a style="background: #48bf53; cursor: pointer; color: #ffffff; font-size: 14px; font-weight: 700; border-radius: 8px; line-height: 42px; padding: 8px;  text-decoration: none;" href="{1}" target="_blank" rel="noopener noreferrer">Accept Invitation</a><br/><br/>"If you have trouble using the "Accept invitation" button, please try the following:<br/>  1. Copy the link below.<br/>  2. Open a new browser window or tab.<br/>  3. Paste the link into the address bar and press Enter.<br/><br/>  <a href="{1}" style="color: blue; text-decoration: underline;">{1}</a></body></html>',
    [UpdatedAt] = GETDATE(),
    [UpdatedBy] = 'System Update - Remove Name Parameter'
WHERE [Id] = '134BD81A-B08B-45C4-80E0-78F58AC20BD4'
  AND [Type] = 1; -- EmailTemplateType.UserActivation

-- Verify the update
SELECT [Id], [Type], [Subject], [Body], [UpdatedAt], [UpdatedBy]
FROM [Internal].[EmailTemplate] 
WHERE [Id] = '134BD81A-B08B-45C4-80E0-78F58AC20BD4';
