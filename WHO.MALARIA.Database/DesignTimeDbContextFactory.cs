using System;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Database
{
    public abstract class DesignTimeDbContextFactory<TContext>
        : IDesignTimeDbContextFactory<TContext> where TContext : DbContext
    {
        public TContext CreateDbContext(string[] args)
        {
            return Create(Directory.GetCurrentDirectory(),
                Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"));
        }

        protected abstract TContext CreateNewInstance(DbContextOptions<TContext> options);

        private TContext Create(string basePath, string env)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(basePath)
                .AddJsonFile("appsettings.json", false, true)
                .AddJsonFile($"appsettings.{env}.json", true)
                .AddEnvironmentVariables();

            IConfiguration config = builder.Build();

            AppSettings appSettings = new AppSettings();
            config.GetSection(Constants.Startup.AppSettingsSection).Bind(appSettings);

            string connectionString = config.GetConnectionString(Constants.Startup.ConnectionString);

            if (string.IsNullOrWhiteSpace(connectionString))
                throw new InvalidOperationException("Could not found a connection string named 'DefaultConnection'");

            return Create(connectionString);
        }

        private TContext Create(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
                throw new ArgumentException($"{nameof(connectionString)} is null or empty", nameof(connectionString));

            var optionsBuilder = new DbContextOptionsBuilder<TContext>();

            optionsBuilder.UseSqlServer(connectionString);

            var options = optionsBuilder.Options;

            return CreateNewInstance(options);
        }
    }
}
