using Microsoft.EntityFrameworkCore.Migrations;

namespace WHO.MALARIA.Database.Migrations
{
    /// <summary>
    /// Migration to update the User Invitation Email Template to remove the name parameter
    /// and fix the greeting to just say "Hello," instead of "Hello {name},"
    /// </summary>
    public partial class UpdateUserInvitationEmailTemplate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Update the User Invitation Email Template to remove the name parameter
            // Template parameters after update:
            // {0} = Country name (was {1})
            // {1} = Invitation link (was {2})
            migrationBuilder.Sql(@"
                UPDATE [Internal].[EmailTemplate] 
                SET 
                    [Body] = '<!DOCTYPE html><html><body>Hello test,<br/><br/>Congratulations!<br/><br/>You have been invited to WHO-MALARIA Surveillance Toolkit.<br/>Kindly accept the invitation and begin using the application for country {0} by logging in.<br/><br/>Click below link to log in:<br/><br/><a style=""background: #48bf53; cursor: pointer; color: #ffffff; font-size: 14px; font-weight: 700; border-radius: 8px; line-height: 42px; padding: 8px;  text-decoration: none;"" href=""{1}"" target=""_blank"" rel=""noopener noreferrer"">Accept Invitation</a><br/><br/>""If you have trouble using the """"Accept invitation"""" button, please try the following:<br/>  1. Copy the link below.<br/>  2. Open a new browser window or tab.<br/>  3. Paste the link into the address bar and press Enter.<br/><br/>  <a href=""{1}"" style=""color: blue; text-decoration: underline;"">{1}</a></body></html>',
                    [UpdatedAt] = GETDATE()
                WHERE [Id] = '134BD81A-B08B-45C4-80E0-78F58AC20BD4'
                  AND [Type] = 1; -- EmailTemplateType.UserActivation
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Rollback to the previous template format with name parameter
            // Template parameters before update:
            // {0} = User name
            // {1} = Country name  
            // {2} = Invitation link
            migrationBuilder.Sql(@"
                UPDATE [Internal].[EmailTemplate] 
                SET 
                    [Body] = '<!DOCTYPE html><html><body>Hello {{0}},<br/><br/>Congratulations!<br/><br/>You have been invited to WHO-MALARIA Surveillance Toolkit.<br/>Kindly accept the invitation and begin using the application for country {{1}} by logging in.<br/><br/>Click below link to log in:<br/><br/><a style=""background: #48bf53; cursor: pointer; color: #ffffff; font-size: 14px; font-weight: 700; border-radius: 8px; line-height: 42px; padding: 8px;  text-decoration: none;"" href=""{{2}}"" target=""_blank"" rel=""noopener noreferrer"">Accept Invitation</a><br/><br/>""If you have trouble using the """"Accept invitation"""" button, please try the following:<br/>  1. Copy the link below.<br/>  2. Open a new browser window or tab.<br/>  3. Paste the link into the address bar and press Enter.<br/><br/>  <a href=""{{2}}"" style=""color: blue; text-decoration: underline;"">{{2}}</a></body></html>',
                    [UpdatedAt] = GETDATE()
                WHERE [Id] = '134BD81A-B08B-45C4-80E0-78F58AC20BD4'
                  AND [Type] = 1; -- EmailTemplateType.UserActivation
            ");
        }
    }
}
