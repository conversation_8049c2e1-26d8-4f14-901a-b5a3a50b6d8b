# External Authentication Fix Summary

## Problem Analysis

The "Request is blocked" error for external account authentication was caused by several configuration issues:

1. **Single-tenant Azure AD Configuration**: The application was configured to use a tenant-specific endpoint (`https://login.windows.net/{tenantId}`) which only allows users from that specific Azure AD tenant.

2. **Missing Production BaseUrl**: The production configuration was missing the `BaseUrl` property, causing redirect URI mismatches.

3. **Poor Error Handling**: The authentication error handling was too generic, making debugging difficult.

4. **Content Security Policy**: The CSP headers were potentially blocking Azure AD authentication requests.

5. **Sign-out Configuration Issue**: The sign-out process was trying to construct URLs with both the common endpoint and tenant ID, causing malformed URLs like `https://login.microsoftonline.com/common/76d22fc8-2330-45cf-ab36-51074cf8f1e2/.well-known/openid-configuration`.

## Implemented Fixes

### 1. Multi-tenant Azure AD Configuration

**File**: `WHO.MALARIA.Domain/Constants/Constants.cs`

- Changed Azure AD authority from `https://login.windows.net/` to `https://login.microsoftonline.com/common/`
- This enables multi-tenant authentication for external accounts

**File**: `WHO.MALARIA.Web/Extensions/IdentityServerSetupExtension.cs`

- Updated to use the common endpoint instead of appending tenant ID
- Explicitly set MetadataAddress to prevent tenant ID from being appended
- Added IssuerAddress validation in OnRedirectToIdentityProvider to fix login challenges
- Disabled SignOutScheme and sign-out callback paths to prevent remote sign-out
- Added event handler to prevent remote sign-out and avoid tenant ID issues
- This allows authentication for users from any Azure AD tenant and Microsoft personal accounts

### 2. Production Configuration Update

**File**: `WHO.MALARIA.Web/bin/Release/net8.0/appsettings.Production.json`

- Added `"BaseUrl": "https://malsurtoolkit-dev.who.int"` to the AzureAD configuration
- This ensures redirect URIs match the production domain

### 3. Enhanced Error Handling and Logging

**File**: `WHO.MALARIA.Web/Areas/idp/Account/ExternalController.cs`

- Added comprehensive error logging in the `Callback()` method
- Added detailed authentication flow logging
- Added specific Azure AD error parameter handling
- Improved exception details for debugging
- Fixed sign-out method to avoid tenant ID issues in multi-tenant scenarios
- Disabled the old SignOut method that was still calling Azure AD sign-out
- Implemented local-only sign-out to avoid remote Azure AD sign-out issues

### 4. Content Security Policy Update

**File**: `WHO.MALARIA.Web/Middlewares/SecurityHeadersMiddleware.cs`

- Added Azure AD endpoints to the CSP `connect-src` directive
- Allows connections to `https://login.microsoftonline.com` and `https://graph.microsoft.com`

## Azure AD App Registration Requirements

To complete the fix, the Azure AD app registration must be updated:

### 1. Enable Multi-tenant Support

- In Azure Portal, go to your app registration
- Under "Authentication" → "Supported account types"
- Select "Accounts in any organizational directory (Any Azure AD directory - Multitenant) and personal Microsoft accounts"

### 2. Update Redirect URIs

Add the following redirect URIs to your Azure AD app registration:

- `https://malsurtoolkit-dev.who.int/signin-aad`
- `https://malsurtoolkit-dev.who.int/signout-callback-aad`
- `https://malsurtoolkit-dev.who.int/signout-aad`

### 3. API Permissions

Ensure the following permissions are granted:

- Microsoft Graph: `User.Read` (Delegated)
- Microsoft Graph: `email` (Delegated)
- Microsoft Graph: `openid` (Delegated)
- Microsoft Graph: `profile` (Delegated)

## Deployment Instructions

### 1. Environment Variables (Recommended)

Instead of modifying appsettings files, set these environment variables in Azure App Service:

```
AppSettings__AzureAD__BaseUrl=https://malsurtoolkit-dev.who.int
AppSettings__AzureAD__ClientId=7cd2419e-ad71-4894-923f-136deabc4d01
AppSettings__AzureAD__ClientSecret=****************************************
AppSettings__AzureAD__TenantId=76d22fc8-2330-45cf-ab36-51074cf8f1e2
```

### 2. Application Restart

After making these changes, restart the application to ensure all configurations are loaded.

### 3. Testing

Test the authentication flow with:

- WHO internal users (@who.int emails)
- External users (non-WHO emails)
- Microsoft personal accounts

## Specific Error Fixed

The main error that was occurring was:

```
System.InvalidOperationException: IDX20803: Unable to obtain configuration from: 'https://login.microsoftonline.com/common/v2.0/76d22fc8-2330-45cf-ab36-51074cf8f1e2/.well-known/openid-configuration'
```

This was caused by:

1. The OpenID Connect middleware trying to append the tenant ID to the common endpoint, creating a malformed URL
2. Multiple SignOut methods in the ExternalController, with one still calling the Azure AD sign-out
3. The application attempting to perform remote sign-out to Azure AD during logout
4. The authentication configuration reverting to use tenant-specific authority URL during login challenges

## Expected Behavior After Fix

1. **WHO Users**: Should authenticate and be granted appropriate access based on their role
2. **External Users**: Should authenticate successfully and be redirected to the unregistered user page (as designed)
3. **Development Users**: The special case for `<EMAIL>` should continue to work in development
4. **Sign-out**: Should work without errors and redirect users to the home page

## Monitoring and Debugging

The enhanced logging will now provide detailed information about:

- Authentication success/failure reasons
- User claims and provider information
- Azure AD error responses
- Redirect URI validation

Check the application logs for entries prefixed with:

- `[EXTERNAL-AUTH-ERROR]`
- `[AZURE-AD-ERROR]`
- `[EXTERNAL-AUTH-SUCCESS]`
- `[EXTERNAL-AUTH-CLAIMS]`

## Security Considerations

- The multi-tenant configuration allows any Azure AD user to attempt authentication
- The application logic still controls access based on email domain and user registration
- WHO users are automatically granted access, while external users must be registered
- The CSP has been updated to allow necessary Azure AD connections while maintaining security
