using System;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Builder;
using Serilog;
using Serilog.Sinks.MSSqlServer;
using Serilog.Events;
using WHO.MALARIA.Domain.Constants;
using System.IO;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Web.Extensions;
using WHO.MALARIA.Web.Middlewares;
using WHO.MALARIA.Web.PipelineBehaviours;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using SendGrid.Extensions.DependencyInjection;
using SendGrid;
using Microsoft.Extensions.Logging;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace WHO.MALARIA.Web
{
    public class Program
    {
        public static void Main(string[] args)
        {
            try
            {
                IConfiguration configuration = GetConfiguration();
                Log.Logger = CreateLogger(configuration);

                var builder = WebApplication.CreateBuilder(args);

                // Add configuration
                LoadConfiguration(builder.Configuration);

                // Add services to the container
                ConfigureServices(builder.Services, configuration, builder.Environment);

                // Configure Serilog
                builder.Host.UseSerilog();

                // Configure Kestrel
                builder.WebHost.ConfigureKestrel(opt =>
                {
                    opt.AddServerHeader = false;
                    opt.Limits.MaxRequestBodySize = null;
                    opt.Limits.KeepAliveTimeout = TimeSpan.FromMinutes(60);
                    opt.Limits.RequestHeadersTimeout = TimeSpan.FromMinutes(60);
                });

                // Configure forwarded headers
              builder.Services.Configure<ForwardedHeadersOptions>(options =>
              {
                  options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto |
                  ForwardedHeaders.XForwardedHost;

                  options.ForwardedHostHeaderName = "X-Original-Host";

                  options.KnownNetworks.Clear();
                  options.KnownProxies.Clear();
              });

                var app = builder.Build();

                // Configure the HTTP request pipeline
                ConfigureApp(app);

                // Apply database migrations automatically
                try
                {
                    // Use the service provider to get the DbContext (recommended approach)
                    using var scope = app.Services.CreateScope();
                    var dbContext = scope.ServiceProvider.GetRequiredService<MalariaDbContext>();

                    // Apply database migrations
                    dbContext.Database.Migrate();
                    Log.Information("Database migrated successfully.");
                }
                catch (Exception ex)
                {
                    Log.Fatal(ex, "An error occurred while migrating the database.");
                    throw; // Re-throw if you want the app to stop on migration failure
                }

                Log.Information("Getting the Web Application running ({ApplicationContext})...", Constants.SeriLog.AppName);
                app.Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Host terminated unexpectedly ({ApplicationContext})", Constants.SeriLog.AppName);
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        private static void ConfigureServices(IServiceCollection services, IConfiguration configuration, IWebHostEnvironment env)
        {
            var appSettingConfiguration = new AppSettings();
            configuration.Bind(Constants.Startup.AppSettings, appSettingConfiguration);

            //configure section of custom config json file
            services.Configure<DQAExcelSetting>(configuration.GetSection(DQAExcelSetting.DQASetting));
            services.Configure<QuestionBankExcelSetting>(configuration.GetSection(QuestionBankExcelSetting.QuestionBankSetting));
            services.Configure<AnalyticalOutputExcelSetting>(configuration.GetSection(AnalyticalOutputExcelSetting.AnalyticalOutputSetting));
            services.Configure<ShellTableExcelSetting>(configuration.GetSection(ShellTableExcelSetting.ShellTableSetting));
            services.AddMemoryCache();
            services.Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto |
                ForwardedHeaders.XForwardedHost;

                // Azure App Service specific headers
                options.ForwardedHostHeaderName = "X-Original-Host";
                options.ForwardedProtoHeaderName = "X-Forwarded-Proto";

                // Trust all proxies for Azure App Service
                options.KnownNetworks.Clear();
                options.KnownProxies.Clear();

                // Required for Azure App Service
                options.RequireHeaderSymmetry = false;
                options.ForwardLimit = null;
            });

            services.AddSingleton(appSettingConfiguration);
            services.AddControllers().AddNewtonsoftJson(options =>
            {
                // Configure date handling for consistent parsing
                options.SerializerSettings.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.IsoDateFormat;
                options.SerializerSettings.DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Utc;
                options.SerializerSettings.DateParseHandling = Newtonsoft.Json.DateParseHandling.DateTime;
            });
            services.AddMvc(options => { options.EnableEndpointRouting = true; });

            // adding swagger
            services.AddSwaggerDocumentation();

            services.AddHttpContextAccessor();

            // adding database context
            services.AddSqlDbContext(configuration);

            services.AddLogging();

            // adding log behaviour pipeline
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehaviour<,>));

            // fixes the issue with google chrome and safari for same site cookies issue
            services.ConfigureNonBreakingSameSiteCookies();

            // identity serve client set up
            services.Setup();
            services.ClientSetup(appSettingConfiguration);

            //Add DIs
            services.RegisterDependencies(configuration);

            services.AddMvc(option => option.EnableEndpointRouting = false);

            // adding static file for spa in production
            services.AddSpaStaticFiles(env);

            // Configure SendGrid only if API key is available
            string sendGridApiKey = appSettingConfiguration.SendGrid.ApiKey ?? null;
            if (!string.IsNullOrWhiteSpace(sendGridApiKey))
            {
                services.AddSendGrid(options =>
                {
                    options.ApiKey = sendGridApiKey;
                    options.HttpErrorAsException = true;
                });
            }
            else
            {
                // Register a dummy SendGrid client to prevent DI errors
                services.AddTransient<ISendGridClient>(provider =>
                {
                    // Note: SendGrid API key is not configured. Email functionality will be disabled.
                    return new SendGridClient("dummy-key-for-di"); // This won't be used due to our EmailService changes
                });
            }
        }

        private static void ConfigureApp(WebApplication app)
        {
            var env = app.Environment;
            var tempDataProvider = app.Services.GetRequiredService<ITempDataProvider>();
            var appSettings = app.Services.GetRequiredService<AppSettings>();


            app.UseForwardedHeaders();

            app.UseSecurityHeaders();

            app.UseIdentityServer();

            //Enable middleware to handle exception
#if DEBUG
            app.ConfigureExceptionHandler(false, tempDataProvider, app.Configuration, appSettings);
#else
            app.ConfigureExceptionHandler(true, tempDataProvider, app.Configuration, appSettings);
            app.UseSpaStaticFiles();

            // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            app.UseHsts();
#endif

            app.UseHttpsRedirection();
            app.UseAntiXssMiddleware();
            app.UseVirusScan();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseSwaggerDocumentation();

            // Request logging middleware - development only
#if DEBUG
            app.Use(async (context, next) =>
            {
                await next.Invoke();
            });
#endif

            // Production debugging middleware for authentication issues
            if (app.Environment.IsProduction())
            {
                app.Use(async (context, next) =>
                {
                    
                    await next.Invoke();
                });
            }
            // mvc and area routing
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(name: "default", pattern: "{controller}/{action}/{id?}");
                endpoints.MapAreaControllerRoute(name: "area", areaName: "idp", pattern: "{area:exists}/{controller}/{action}/{id?}");
            });

            // In development mode use 'npm start' command
            app.UseSpa(env);
        }


        /// <summary>
        /// Builds the Configuration framework
        /// </summary>
        /// <returns></returns>
        private static IConfiguration GetConfiguration()
        {
            IConfigurationBuilder builder = new ConfigurationBuilder();
            builder = LoadConfiguration(builder);

            return builder.Build();
        }


        /// <summary>
        /// Loads configuration information from appSettings.json, configuration.json and environment variables; including environment specific file variants
        /// </summary>
        private static IConfigurationBuilder LoadConfiguration(IConfigurationBuilder builder)
        {
            //CAUTION: Linux file system is case sensitive. So the name of the configuration file must EXACTLY match the value assigned to environment variable.
            string environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            builder
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{environment}.json", optional: true, reloadOnChange: true)
                .AddJsonFile("excelsettings.json", optional: false, reloadOnChange: true) //  Adding custom config json file
                .AddEnvironmentVariables();
                
            // Add user secrets in development environment
            if (environment == "Development")
            {
                builder.AddUserSecrets<Program>();
            }

            return builder;
        }

        /// <summary>
        /// Serilog configuration for file and database
        /// </summary>
        /// <param name="host"> Host </param>
        /// <param name="Configuration"> Appsetting configuration </param>
        /// <returns> Logger configuration </returns>
        private static Serilog.ILogger CreateLogger(IConfiguration configuration)
        {
            var columnOption = new ColumnOptions();
            columnOption.Store.Remove(StandardColumn.MessageTemplate);
            columnOption.Store.Remove(StandardColumn.Properties);
            columnOption.Store.Add(StandardColumn.LogEvent);

            var connectionString = configuration.GetConnectionString(Constants.Startup.ConnectionString);

            return new LoggerConfiguration()
                .Enrich.WithProperty("ApplicationContext", Constants.SeriLog.AppName)
                .WriteTo.Console()
                .WriteTo.File(configuration[Constants.SeriLog.SerilogFilePath] + DateTime.Now.Day + "_" + DateTime.Now.Month + "_" + DateTime.Now.Year + ".log",
                    restrictedToMinimumLevel: LogEventLevel.Information, outputTemplate: configuration[Constants.SeriLog.SerilogOutputFileTemplate])
                .WriteTo.MSSqlServer(connectionString, Constants.SeriLog.LogTableName,
                    restrictedToMinimumLevel: LogEventLevel.Information, columnOptions: columnOption, autoCreateSqlTable: true)
                .ReadFrom.Configuration(configuration)
                .CreateLogger();
        }
    }
}
