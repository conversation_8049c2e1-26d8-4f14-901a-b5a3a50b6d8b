using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features;
using WHO.MALARIA.Features.Helpers;

namespace WHO.MALARIA.Services.Subscribers
{
    /// <summary>
    /// Handles the notification event that is being triggered when the user's role changes.
    /// </summary>
    public class UserRoleAssignmentEmailNotificationHandler : RuleBase, INotificationHandler<UserRoleAssignmentEmailNotification>
    {
        private readonly IEmailService _emailService;
        private readonly ILogger<UserRoleAssignmentEmailNotification> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly AppSettings _appSettings;

        public UserRoleAssignmentEmailNotificationHandler(IEmailService emailService,
                                                          ILogger<UserRoleAssignmentEmailNotification> logger,
                                                          IUnitOfWork unitOfWork,
                                                          IHttpContextAccessor httpContextAccessor,
                                                          AppSettings appSettings)
        {
            _emailService = emailService;
            _logger = logger;
            _unitOfWork = unitOfWork;
            _httpContextAccessor = httpContextAccessor;
            _appSettings = appSettings;
        }

        /// <summary>
        /// Fetches the email template, formats the email body and sends the email to the user
        /// </summary>
        /// <param name="notification">Object of UserRoleAssignmentEmailNotification class</param>
        /// <param name="cancellationToken">Used to cancel the current operation</param>
        public async Task Handle(UserRoleAssignmentEmailNotification notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation(Constants.SeriLog.InformationLogMessageTemplate, this.GetType().Name, UtilityHelper.GetCallerMethodName(), notification);
                       
            EmailTemplate emailTemplate = await _unitOfWork.EmailTemplateRepository
                                              .Queryable(et => et.Type == GetTemplateType(notification.UserType))
                                              .SingleAsync();

            // Use configured BaseUrl instead of current request URL to ensure correct domain in email links
            string applicationUrl = _appSettings?.AzureAD?.BaseUrl ?? _httpContextAccessor.HttpContext.GetApplicationURL();
            string emailBody = string.Format(emailTemplate.Body, notification.Country, applicationUrl);

            await _emailService.SendEmail(new string[] { notification.Email }, emailTemplate.Subject, emailBody);
        }

        /// <summary>
        ///  Get email template type
        /// </summary>
        /// <param name="userRole">user role</param>
        /// <returns>template type</returns>
        public static int GetTemplateType(UserRoleEnum userRole)
        {
            switch (userRole)
            {
                case UserRoleEnum.SuperManager:
                    return (int)EmailTemplateType.SuperManagerRoleAssignment;

                case UserRoleEnum.WHOAdmin:
                    return (int)EmailTemplateType.WHOAdminRoleAssignment;

            }

            throw new Exception($"{MethodBase.GetCurrentMethod().Name} - Can not return template type for user role :- {EnumHelper.GetDisplayUserType((int)userRole)}");
        }
    }
}
