namespace WHO.MALARIA.Domain.Constants
{
    public static class Constants
    {

        public static class Startup
        {
            public const string MediatRAssemblyName = "WHO.MALARIA.Services";
            public const string ConnectionString = "DefaultConnection";
            public const string SpaBuildFolderName = "malaria-client/build";
            public const string SpaProjectName = "malaria-client";
            public const string AppSettingsSection = "AppSettings";
            public const string ApplicationCookieName = "Authentication";
            public const string AppSettings = "AppSettings";
        }

        public static class GlobalDashboardSummaryCacheKeys
        {
            public const string GlobalDashboardIndicatorSummaryResponsesJson = "GlobalDashboardIndicatorSummaryResponsesJson";
            public const string GlobalDashboardObjectiveMapSummaryResponseJson = "GlobalDashboardObjectiveMapSummaryResponseJson";
            public const string GlobalDashboardRegionalSummaryResponseJson = "GlobalDashboardRegionalSummaryResponseJson";
        }

        public static class Common
        {
            public const string DefaultLanguage = "en";
            public const string I18next = "i18next";
            public const string NewUserCookieName = "WHO.MALARIA.IsNewUser";
            public const string UserInfoCookieName = "WHO.MALARIA.UserInfo";
            public const string Production = "Production";
            public const string WHOUser = "WHOUser";
            public const string ActiveDirectoryUserDetailsKey = "ActiveDirectoryUserDetailsKey";
            public const string EmailFormatRegex = @"^[\p{L}0-9!$'*+\-_]+(\.[\p{L}0-9!$'*+\-_]+)*@[\p{L}0-9-]+(\.[\p{L}0-9-]+)*(\.[\p{L}]{2,})$";
            public const string Admin = "Admin";
            public const string ExcelDateFormat = "dd\\/MM\\/yyyy";
            public static string[] Months = { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" };
            public static string[] Months_fr = { "janvier", "février", "mars", "avril", "mai", "juin", "juillet", "août", "septembre", "octobre", "novembre", "décembre" };
            public const string Other = "other";
            public const long DiagramMaxFileSize = 10485760;
            public static string[] AllowedDiagramFileTypes = { ".png", ".jpg", ".jpeg",".pdf" };
            public static string[] ValidExcelExtensions = { ".xlsx", ".xls" };
            public static string[] ValidZipFileExtensions = { ".zip" };
            public static string[] ValidDataAnalyisFileExtensions = { ".png", ".jpg", ".jpeg", ".xlsx", ".xls", ".doc",".pdf" };
            public static string OrganizationName = "WHO";
            public static string Yes = "Yes";
            public static string No = "No";
            public static string IndicatorYes = "Common.Yes";
            public static string IndicatorNo = "Common.No";
            public const string Year = "Common.Year";
            public const string Percentage = "Common.Percentage";
            public const string Region = "Common.Region";
            public const string ProportionAvailable = "Common.ProportionAvailable";
            public const string Community = "Common.Community";
            public const string PrivateInformal = "Common.PrivateInformal";
            public const string Public = "Common.Public";
            public const string HealthFacilities = "Common.HealthFacilities";
            public const string Laboratory = "Common.Laboratory";
            public const string Hospital = "Common.Hospital";
            public const string PrivateFormalText = "Common.PrivateFormalText";
            public const string FaithBasedClinics = "Common.FaithBasedClinics";
            public const string NonGovernmentalOrganizationClinics = "Common.NonGovernmentalOrganizationClinics";
            public const string Military = "Common.MilitaryRowHeading";
            public const string Police = "Common.PoliceRowHeading";
            public const string Prison = "Common.Prison";
            public const string Rate = "Common.Rate";
            public const string Width40 = "40";
            public const string Width50 = "50";
            public const string Width60 = "60";
            public const string Width80 = "80";
            public const string Width10 = "10";
            public const string Width100 = "100";
            public const string Width120 = "120";
            public const string Width150 = "150";
            public const string Width180 = "180";
            public const string Width200 = "200";
            public const string Width300 = "300";
            public const string Width400 = "400";
            public const string Width250 = "250";

            public const long ExcelFileSizeBytes = 26214400; //25MB Size in Bytes

            public const string IndicatorResponseTranslationFileName = "indicators-responses";
            public const string CommonResponseTranslationFileName = "translation";

            public const string DocumentFileName = "MalariaSurveillanceToolkitTools.zip";
            public const string DocumentUploadFilePath = "Assets";
            public const string TotalColumnName = "Total";
            public const string InvitationSubject = "WHO has invited you to access applications within their organization";
        }

        public static class DbOperators
        {
            public new const string Equals = "=";
            public const string NotEquals = "<>";
        }

        public static class SeriLog
        {
            public const string AppName = "Web Application";
            public const string LogTableName = "ErrorLog";
            public const string SerilogFilePath = "SerilogFilePath:FilePath";
            public const string SerilogOutputFileTemplate = "SerilogOutputFileTemplate:outputTemplate";
            public const string InformationLogMessageTemplate = "{class}.{method} - {@parameters}";
        }

        public static class IdentityConstant
        {
            // Status of web service account which is used while validate web service account
            public const string IsUserActive = "IsUserActive";

            // Status(Active status) value of web service account which is used while validate web service account
            public const string True = "True";
            public const string AzureActiveDirectory = "aad";
            public const string AzureActiveDirectoryAuthority = "https://login.windows.net/";
            public const string AzureActiveDirectoryResource = "https://graph.microsoft.com";

            public const string External = "External";
            public const string Internal = "Internal";
            public const string WhoOrganizationName = "WHO";
        }

        public static class IdentityClaims
        {
            // claim name for identity provider
            public const string IdpClaimName = "idp";

            //The name of Primary sid claim to store the user id for identity 
            public const string PrimarySidClaim = "http://schemas.microsoft.com/ws/2008/06/identity/claims/primarysid";

            //The name of Subject claim to store the Identity id 
            public const string SubClaim = "sub";

            // The client id which is requesting for the api
            public const string ClientId = "client_id";

            // The name of claim which sets the logged-in user name .
            public const string Name = "name";

            // Language selected by user . It is associated with the Identity and not with the separate user accounts. 
            public const string LanguageClaim = "language";

            // The name of claim which sets the account name of the logged in user.
            public const string AccountName = "account_name";

            // User actual name from associated inventory record. 
            public const string UsernameClaim = "username";

            public const string UserType = "UserType";

            public const string Email = "email";

            public const string CountriesRequested = "countries_requested";
        }

        public static class AuthenticationSchemes
        {
            //The name of the authentication scheme to use for selecting one out of many User records linked to an Identity
            public const string SelectUserScheme = "Identity.SelectUserScheme";

            //The name of the authentication scheme when 2FA is enabled
            public const string TwoFactorUserIdScheme = "Identity.TwoFactorUserId";

            //The name of the authentication scheme when user is logging-in with two factor authentication.
            public const string AllowedAuthenticationSchemesFor2FA = "Identity.Application,Identity.TwoFactorUserId";

            //The name of the authentication scheme when external client is accessing the APIs.
            public const string ExternalApplication = "ExternalApplication";
        }

        public static class DownloadDocument
        {
            public const string ContentType = "application/zip";
            public const string MalariaSurveillanceToolkitToolsFolderPath = @"\Assets\{0}\MalariaSurveillanceToolkitTools.zip";
            public const string ExcelFormat = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            public const string SLDQATemplateFile = "ServiceLevel_DQA";
            public const string QuestionBankTemplateFileName = "Questionnaires";
            public const string AnalyticalOutputTemplateFileName = "AnalyticalOutput";
            public const string ObjectiveDiagrams = "ObjectiveDiagrams.zip";
            public const string SubObjectiveDiagrams = "SubObjectiveDiagrams.zip";
            public const string QuestionBank = "QuestionBank.zip";
            public const string ShellTable = "ShellTable";
            public const string HealthFacilityFilePath = @"\Templates\QuestionBank\Health Facility Template";
            public const string HealthFacilityFileName = "Health Facility Template.xlsx";
            public const string HealthFacilityTemplateFileName = "Health Facility Template";
            public const string ScoreCardFileName = "ScoreCard.xlsx";
            public const string ContentTypeForExcel = "application/octet-stream";
            public const string OverviewFilePath = @"\Assets\{0}\Introduction to the Malaria Surveillance Assessment Toolkit.pdf";
            public const string PDFContentType = "application/pdf";
            public const string SurveryData = "Survey_Data";
            public const string DQAEliminationTemplateFilePath = @"\Templates\DQA\DQA_Elimination.xlsx";
            public const string DQAEliminationFileName = "DQA_Elimination.xlsx";
            public const string DQAEliminationTemplateFilePath_FR = @"\Templates\DQA\DQA_Elimination_FR.xlsx";
        }

        public static class SqlExceptionUserFriendlyMessage
        {
            public const string IncorrectSyntax = "Incorrect syntax in sql query.";
            public const string InvalidColumnName = "Invalid database column name tried to be accessed.";
            public const string NotNullViolation = "Null value cannot be inserted to a non nullable field.";
            public const string ConversionFailed = "Conversion failed.";
            public const string ConstraintViolation = "One of the constraints is violated.";
            public const string ColumnSizeViolation = "Cannot insert or update a row because total variable column size, including overhead, is more than the expected limit.";
            public const string UniqueKeyConstraintViolation = "Duplicate value is attempted for unique column.";
            public const string MergeStatementConflict = "MERGE statement failed in the query.";
            public const string DeleteConflict = "Unable to delete due to conflict.";
            public const string RequiredFieldNotSupplied = "Missing parameter in query.";
            public const string StringOrBinaryDataTruncated = "Maximum characters limit has been reached.";
            public const string EnteredValuesLengthExceedThanColumnLength = "Entered value has exceeded the column length.";
            public const string UserAccessDenied = "User access is denied for the requested database or database object.";
            public const string MultiPartIdentifierNotMapped = "Multi-part identifier could not be mapped to an existing entity. Please check the query configured.";
        }

        public static class Exception
        {
            public const string InvalidValue = "InvalidValue";
            public const string PermissionDeniedToFinalize = "PermissionDeniedToFinalize";
            public const string FieldShouldNotBeEmpty = "FieldShouldNotBeEmpty";
            public const string IdentityWithEmailAlreadyExist = "IdentityWithEmailAlreadyExist";
            public const string EmailIsNotValid = "EmailIsNotValid";
            public const string OnlySuperManagerAccess = "OnlySuperManagerAccess";
            public const string OnlyViewerAccess = "OnlyViewerAccess";
            public const string SuperManagerAlreadyPresent = "SuperManagerAlreadyPresent";
            public const string AccessToOnlyWHOADUser = "AccessToOnlyWHOADUser";
            public const string AccessToOnlyWHOUser = "AccessToOnlyWHOUser";
            public const string UserStatusMustBePending = "UserStatusMustBePending";
            public const string InvalidUserType = "InvalidUserType";
            public const string UserMustBeSuperManagerOfCountry = "UserMustBeSuperManagerOfCountry";
            public const string PasswordShouldBeStrong = "PasswordShouldBeStrong";
            public const string CustomerEmailAlreadyExist = "CustomerEmailAlreadyExist";
            public const string UserCantRequestOtherCountry = "UserCantRequestOtherCountry";
            public const string UserNameAlreadyExists = "UserNameAlreadyExists";
            public const string InvalidIndicatorsSelectedForStrategy = "InvalidIndicatorsSelectedForStrategy";
            public const string NoEditPermissionOnAssessment = "NoEditPermissionOnAssessment";
            public const string AssessmentDoesNotExist = "AssessmentDoesNotExist";
            public const string InvalidDateRange = "InvalidDateRange";
            public const string UpdateAssessmentDateIsInvalid = "UpdateAssessmentDateIsInvalid";
            public const string CheckForDuplicateUserCountryAccess = "UserAlreadyRequestedForCountry";
            public const string AssessmentAlreadyCreatedForTheCountryForYear = "AssessmentAlreadyCreatedForTheCountryForYear";
            public const string InvalidUserTypeUpdateBySuperManager = "InvalidUserTypeUpdateBySuperManager";
            public const string RestrictToDeactivateManager = "RestrictToDeactivateManager";
            public const string AtleastOneOptionalIndicatorBeSelected = "AtleastOneOptionalIndicatorBeSelected";
            public const string InvalidMonth = "InvalidMonth";
            public const string InvalidYear = "InvalidYear";
            public const string InvalidFile = "InvalidFile";
            public const string InvalidMonthRange = "InvalidMonthRange";
            public const string NoPermissionToCreateTemplate = "NoPermissionToCreateTemplate";
            public const string NoPermissionToUpload = "NoPermissionToUpload";
            public const string NoPermissionToGenerateQuestionBankTemplate = "NoPermissionToGenerateQuestionBankTemplate";
            public const string NoPermissionToExportShellTableTemplate = "NoPermissionToExportShellTableTemplate";
            public const string NoPermissionToUpdateObservedDataQualityReason = "NoPermissionToUpdateObservedDataQualityReason";
            public const string NoPermissionToUploadDiagram = "NoPermissionToUploadDiagram";
            public const string SelectFile = "SelectFile";
            public const string CanNotProcessTemplate = "CanNotProcessTemplate";
            public const string InvalidRegisterTypeSelected = "InvalidRegisterTypeSelected";
            public const string UserDoesNotHavePermission = "UserDoesNotHavePermission";
            public const string ServiceLevelIsFinalized = "ServiceLevelIsFinalized";
            public const string ServiceLevelDoesNotExist = "ServiceLevelDoesNotExist";
            public const string InvalidFileSize = "InvalidFileSize";
            public const string FileSizeGreaterThan10MB = "FileSizeGreaterThan10MB";
            public const string InvalidDiagramFile = "InvalidDiagramFile";
            public const string TemplateEmpty = "TemplateEmpty";
            public const string DuplicateDataSourceSelected = "DuplicateDataSourceSelected";
            public const string DuplicateTextForOtherDataSystemSource = "DuplicateTextForOtherDataSystemSource";
            public const string InvalidStartPeriodDate = "InvalidStartPeriodDate";
            public const string OnlyServiceLevelRecordPresent = "OnlyServiceLevelRecordPresent";
            public const string InvalidVariableIds = "InvalidVariableIds";
            public const string InvalidDRResponseStatus = "InvalidDRResponseStatus";
            public const string ResponseMappingKeyIsNotFound = "ResponseMappingKeyIsNotFound";
            public const string InvalidYearMonthRange = "InvalidYearMonthRange";
            public const string AtleastOneDiagramIsReqToUpload = "AtleastOneDiagramIsReqToUpload";
            public const string InvalidInvitationLink = "InvalidInvitationLink";
            public const string SameDomainAsAzureADCannotBeInvited = "SameDomainAsAzureADCannotBeInvited";
            public const string InSufficientPrivilegesToAddUser = "InSufficientPrivilegesToAddUser";
            public const string InSufficientPrivilegesToAddExistingWHOUser = "InSufficientPrivilegesToAddExistingWHOUser";
            public const string InSufficientPrivilegesToSendInvite = "InSufficientPrivilegesToSendInvite";
            public const string ContainExecutables = "ContainExecutables";
            public const string ContainsScript = "ContainsScript";
            public const string ContainsPasswordProtectedFile = "ContainsPasswordProtectedFile";
            public const string ContainsRestrictedFileFormat = "ContainsRestrictedFileFormat";
            public const string ContainsMacros = "ContainsMacros";
            public const string ContainsXmlExternalEntities = "ContainsXmlExternalEntities";
            public const string ContainsInsecureDeserialization = "ContainsInsecureDeserialization";
            public const string ContainsHtml = "ContainsHtml";
            public const string VirusScanFailed = "VirusScanFailed";
            public const string InvalidRespondentTypeSelected = "InvalidRespondentTypeSelected";
            public const string UserHasActiveAssessmentAsManager = "UserHasActiveAssessmentAsManager";
            public const string AtLeastOneVariableShouldBeSelectedForConcordance = "AtLeastOneVariableShouldBeSelectedForConcordance";
            public const string AtLeastOnePriorityVariableShouldBeSelected = "AtLeastOnePriorityVariableShouldBeSelected";
            public const string DataSourceCannotBeSame = "DataSourceCannotBeSame";
            public const string MultipleDistrictsHaveSameDistrictCode = "MultipleDistrictsHaveSameDistrictCode";
            public const string MultipleHealthFacilitiesHaveSameCode = "MultipleHealthFacilitiesHaveSameCode";
            public const string MultipleRecordsPresentInHealthFacilitySheet = "MultipleRecordsPresentInHealthFacilitySheet";
            public const string SameValuesPresentForMultipleRowsInHealthFacilitySheet = "SameValuesPresentForMultipleRowsInHealthFacilitySheet";
            public const string SuperManagerCountrySameAsHealthFacility = "SuperManagerCountrySameAsHealthFacility";
            public const string NoRecordsPresentInHealthFacilitySheet = "NoRecordsPresentInHealthFacilitySheet";
            public const string InvalidHealthFacilityType = "InvalidHealthFacilityType";
            public const string InvalidHealthFacilitySheetData = "InvalidHealthFacilitySheetData";
            public const string InvalidHealthFacilitySheet = "InvalidHealthFacilitySheet";
            public const string MultipleCountriesNotAllowedInHealthFacilitySheet = "MultipleCountriesNotAllowedInHealthFacilitySheet";
            public const string MalariaToolKitFileName = "MalariaToolKitFileName";
            public const string NoHealthFacilityData = "NoHealthFacilityData";
            public const string WHO_INT_UsersNotAllowedUseToRegister = "WHO_INT_UsersNotAllowedUseToRegister";
            public const string InvalidHealthFacility = "InvalidHealthFacility";
            public const string AccessTokenExpiredWHOADUser = "AccessTokenExpiredWHOADUser";
            public const string InSufficientPrivilegesToSendEmail = "InSufficientPrivilegesToSendEmail";
        }

        public static class DQADataSources
        {
            public const string Other = "Other";
            public const string HMIS = "HMIS/DHIS 2";
            public const string IDSR = "IDSR";
            public const string Laboratory = "Laboratory";
            public const string VitalRegistration = "Vital Registration";
            public const string Other_FR = "Autre";
            public const string HMIS_FR = "HMIS/DHIS 2";
            public const string IDSR_FR = "IDSR";
            public const string Laboratory_FR = "Laboratoire";
            public const string VitalRegistration_FR = "Enregistrement Vital";
        }

        public static class HealthFacility
        {
            public const string CanNotInsertDuplicateValueOfDistrictCodeForCountry = "You can not insert duplicate value for district code in health facility for same country.";
            public const string CanNotInsertDuplicateValueOfCodeForCountry = "You can not insert duplicate value for code in health facility for same country.";
        }

        public static class SqlExceptionErrorCodes
        {
            public const short ForeignKeyConflict = 547;
            public const short UniqueKeyViolation = 2601;
        }

        public static class MonitoringFrequencyType
        {
            public const string Quarterly = "Quarterly";
            public const string Monthly = "Monthly";
            public const string Weekly = "Weekly";
            public const string Annually = "Annually";
            public const string Biannually = "Biannually";
        }

        public static class TranslationFileNamesCacheKeys
        {
            public const string IndicatorResponseEnglishTranslationJson = "IndicatorResponseEnglishTranslationJson";
            public const string IndicatorResponseFrenchTranslationJson = "IndicatorResponseFrenchTranslationJson";
            public const string CommonResponseEnglishTranslationJson = "CommonResponseEnglishTranslationJson";
            public const string CommonResponseFrenchTranslationJson = "CommonResponseFrenchTranslationJson";
        }

        public static class IndicatorMetNotMetStatus
        {
            public const string MET = "MET";
            public const string NOT_MET = "NOT MET";
            public const string PARTIALLY_MET = "PARITALLY MET";
            public const string NOT_ASSESSED = "NOT ASSESSED";
        }      
    }
}
